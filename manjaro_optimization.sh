#!/bin/bash
# Script de Optimización Extrema para Manjaro Plasma
# Autor: Optimización del Sistema
# Fecha: $(date)

set -e

echo "🚀 INICIANDO OPTIMIZACIÓN EXTREMA DE MANJARO PLASMA"
echo "=================================================="

# Colores para output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Función para logging
log() {
    echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')] $1${NC}"
}

warning() {
    echo -e "${YELLOW}[WARNING] $1${NC}"
}

error() {
    echo -e "${RED}[ERROR] $1${NC}"
}

# Verificar si se ejecuta como root para ciertas operaciones
check_root() {
    if [[ $EUID -eq 0 ]]; then
        echo "Ejecutándose como root - OK"
    else
        echo "Algunas optimizaciones requieren sudo"
    fi
}

# 1. OPTIMIZACIÓN DE PARÁMETROS DEL KERNEL
optimize_kernel_params() {
    log "1. Optimizando parámetros del kernel..."
    
    # Crear backup del grub actual
    sudo cp /etc/default/grub /etc/default/grub.backup.$(date +%Y%m%d_%H%M%S)
    
    # Parámetros optimizados para rendimiento
    KERNEL_PARAMS="quiet splash udev.log_priority=3 nowatchdog processor.max_cstate=1 intel_idle.max_cstate=0 idle=poll mitigations=off preempt=full transparent_hugepage=always vm.swappiness=10 vm.vfs_cache_pressure=50 vm.dirty_background_ratio=5 vm.dirty_ratio=10"
    
    # Actualizar GRUB
    sudo sed -i "s/GRUB_CMDLINE_LINUX_DEFAULT=.*/GRUB_CMDLINE_LINUX_DEFAULT=\"$KERNEL_PARAMS\"/" /etc/default/grub
    
    log "Parámetros del kernel actualizados. Se aplicarán en el próximo reinicio."
}

# 2. OPTIMIZACIÓN DE SYSCTL
optimize_sysctl() {
    log "2. Configurando parámetros sysctl para máximo rendimiento..."
    
    sudo tee /etc/sysctl.d/99-performance.conf > /dev/null << 'EOF'
# Optimización de memoria y rendimiento
vm.swappiness=10
vm.vfs_cache_pressure=50
vm.dirty_background_ratio=5
vm.dirty_ratio=10
vm.dirty_expire_centisecs=3000
vm.dirty_writeback_centisecs=500
vm.overcommit_memory=1
vm.overcommit_ratio=50

# Optimización de red
net.core.rmem_default=262144
net.core.rmem_max=16777216
net.core.wmem_default=262144
net.core.wmem_max=16777216
net.core.netdev_max_backlog=5000
net.ipv4.tcp_rmem=4096 65536 16777216
net.ipv4.tcp_wmem=4096 65536 16777216
net.ipv4.tcp_congestion_control=bbr

# Optimización del scheduler
kernel.sched_migration_cost_ns=5000000
kernel.sched_autogroup_enabled=0

# Optimización de I/O
vm.page-cluster=0
vm.laptop_mode=0
EOF

    sudo sysctl -p /etc/sysctl.d/99-performance.conf
    log "Parámetros sysctl aplicados."
}

# 3. OPTIMIZACIÓN DE I/O SCHEDULER
optimize_io_scheduler() {
    log "3. Optimizando I/O scheduler..."
    
    # Detectar tipo de disco
    for disk in $(lsblk -d -o name,rota | grep -E "sd|nvme" | awk '{print $1}'); do
        rotation=$(lsblk -d -o name,rota | grep $disk | awk '{print $2}')
        if [[ "$rotation" == "1" ]]; then
            # HDD - usar mq-deadline
            echo "mq-deadline" | sudo tee /sys/block/$disk/queue/scheduler > /dev/null
            log "Configurado mq-deadline para HDD: $disk"
        else
            # SSD/NVMe - usar none (noop)
            echo "none" | sudo tee /sys/block/$disk/queue/scheduler > /dev/null 2>/dev/null || echo "kyber" | sudo tee /sys/block/$disk/queue/scheduler > /dev/null
            log "Configurado none/kyber para SSD: $disk"
        fi
    done
}

# 4. OPTIMIZACIÓN DE PLASMA
optimize_plasma() {
    log "4. Optimizando configuración de Plasma..."
    
    # Crear directorio de configuración si no existe
    mkdir -p ~/.config
    
    # Optimizar animaciones de Plasma
    kwriteconfig5 --file ~/.config/kwinrc --group Compositing --key AnimationSpeed 2
    kwriteconfig5 --file ~/.config/kwinrc --group Compositing --key Backend OpenGL
    kwriteconfig5 --file ~/.config/kwinrc --group Compositing --key GLCore true
    kwriteconfig5 --file ~/.config/kwinrc --group Compositing --key HiddenPreviews 4
    kwriteconfig5 --file ~/.config/kwinrc --group Compositing --key OpenGLIsUnsafe false
    kwriteconfig5 --file ~/.config/kwinrc --group Compositing --key WindowsBlockCompositing true
    
    # Optimizar efectos de escritorio
    kwriteconfig5 --file ~/.config/kwinrc --group Plugins --key blurEnabled false
    kwriteconfig5 --file ~/.config/kwinrc --group Plugins --key contrastEnabled false
    kwriteconfig5 --file ~/.config/kwinrc --group Plugins --key kwin4_effect_fadeEnabled false
    kwriteconfig5 --file ~/.config/kwinrc --group Plugins --key kwin4_effect_translucencyEnabled false
    
    # Optimizar indexación de archivos
    kwriteconfig5 --file ~/.config/baloofilerc --group "Basic Settings" --key "Indexing-Enabled" false
    
    log "Configuración de Plasma optimizada."
}

# 5. OPTIMIZACIÓN DE SERVICIOS
optimize_services() {
    log "5. Optimizando servicios del sistema..."
    
    # Deshabilitar servicios innecesarios (con cuidado)
    SERVICES_TO_DISABLE=(
        "bluetooth.service"
        "cups.service"
        "avahi-daemon.service"
        "ModemManager.service"
    )
    
    for service in "${SERVICES_TO_DISABLE[@]}"; do
        if systemctl is-enabled $service &>/dev/null; then
            warning "¿Deshabilitar $service? (s/N)"
            read -r response
            if [[ "$response" =~ ^[Ss]$ ]]; then
                sudo systemctl disable $service
                sudo systemctl stop $service
                log "Servicio $service deshabilitado."
            fi
        fi
    done
}

# 6. OPTIMIZACIÓN DE MEMORIA
optimize_memory() {
    log "6. Configurando optimizaciones de memoria..."
    
    # Configurar zram si no está configurado
    if ! lsmod | grep -q zram; then
        warning "¿Configurar ZRAM para compresión de memoria? (s/N)"
        read -r response
        if [[ "$response" =~ ^[Ss]$ ]]; then
            sudo modprobe zram
            echo 'zram' | sudo tee -a /etc/modules-load.d/zram.conf
            
            # Configurar zram
            sudo tee /etc/systemd/system/zram.service > /dev/null << 'EOF'
[Unit]
Description=Compressed RAM
After=multi-user.target

[Service]
Type=oneshot
ExecStart=/bin/bash -c 'echo lz4 > /sys/block/zram0/comp_algorithm && echo 8G > /sys/block/zram0/disksize && mkswap /dev/zram0 && swapon -p 10 /dev/zram0'
ExecStop=/bin/bash -c 'swapoff /dev/zram0'
RemainAfterExit=yes

[Install]
WantedBy=multi-user.target
EOF
            
            sudo systemctl enable zram.service
            log "ZRAM configurado."
        fi
    fi
}

# FUNCIÓN PRINCIPAL
main() {
    log "Iniciando optimización del sistema..."
    check_root
    
    echo "Selecciona las optimizaciones a aplicar:"
    echo "1. Parámetros del kernel (requiere reinicio)"
    echo "2. Parámetros sysctl (inmediato)"
    echo "3. I/O scheduler (inmediato)"
    echo "4. Optimización de Plasma (inmediato)"
    echo "5. Servicios del sistema"
    echo "6. Optimización de memoria/ZRAM"
    echo "7. TODAS las optimizaciones"
    echo "0. Salir"
    
    read -p "Ingresa tu opción (0-7): " choice
    
    case $choice in
        1) optimize_kernel_params ;;
        2) optimize_sysctl ;;
        3) optimize_io_scheduler ;;
        4) optimize_plasma ;;
        5) optimize_services ;;
        6) optimize_memory ;;
        7) 
            optimize_kernel_params
            optimize_sysctl
            optimize_io_scheduler
            optimize_plasma
            optimize_services
            optimize_memory
            ;;
        0) exit 0 ;;
        *) error "Opción inválida" ;;
    esac
    
    log "Optimización completada!"
    warning "Algunas optimizaciones requieren reinicio para tomar efecto."
}

# Ejecutar función principal
main "$@"
