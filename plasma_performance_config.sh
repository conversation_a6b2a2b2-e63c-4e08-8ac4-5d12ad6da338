#!/bin/bash
# Configuración de rendimiento específica para Plasma
# Optimiza KDE Plasma para máximo rendimiento

set -e

echo "⚡ OPTIMIZACIÓN ESPECÍFICA DE PLASMA"
echo "==================================="

# Colores
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log() {
    echo -e "${GREEN}[PLASMA] $1${NC}"
}

warning() {
    echo -e "${YELLOW}[WARNING] $1${NC}"
}

info() {
    echo -e "${BLUE}[INFO] $1${NC}"
}

# 1. OPTIMIZACIÓN DE COMPOSITING
optimize_compositing() {
    log "Optimizando compositor de KWin..."
    
    # Configurar compositor para máximo rendimiento
    kwriteconfig5 --file ~/.config/kwinrc --group Compositing --key Enabled true
    kwriteconfig5 --file ~/.config/kwinrc --group Compositing --key Backend OpenGL
    kwriteconfig5 --file ~/.config/kwinrc --group Compositing --key GLCore true
    kwriteconfig5 --file ~/.config/kwinrc --group Compositing --key GLPlatformInterface glx
    kwriteconfig5 --file ~/.config/kwinrc --group Compositing --key GLTextureFilter 2
    kwriteconfig5 --file ~/.config/kwinrc --group Compositing --key HiddenPreviews 4
    kwriteconfig5 --file ~/.config/kwinrc --group Compositing --key OpenGLIsUnsafe false
    kwriteconfig5 --file ~/.config/kwinrc --group Compositing --key WindowsBlockCompositing true
    kwriteconfig5 --file ~/.config/kwinrc --group Compositing --key AnimationSpeed 2
    
    log "Compositor optimizado para rendimiento."
}

# 2. DESHABILITAR EFECTOS PESADOS
disable_heavy_effects() {
    log "Deshabilitando efectos visuales pesados..."
    
    # Deshabilitar efectos que consumen recursos
    kwriteconfig5 --file ~/.config/kwinrc --group Plugins --key blurEnabled false
    kwriteconfig5 --file ~/.config/kwinrc --group Plugins --key contrastEnabled false
    kwriteconfig5 --file ~/.config/kwinrc --group Plugins --key kwin4_effect_fadeEnabled false
    kwriteconfig5 --file ~/.config/kwinrc --group Plugins --key kwin4_effect_translucencyEnabled false
    kwriteconfig5 --file ~/.config/kwinrc --group Plugins --key slidebackEnabled false
    kwriteconfig5 --file ~/.config/kwinrc --group Plugins --key zoomEnabled false
    kwriteconfig5 --file ~/.config/kwinrc --group Plugins --key magnifierEnabled false
    kwriteconfig5 --file ~/.config/kwinrc --group Plugins --key mouseclickEnabled false
    kwriteconfig5 --file ~/.config/kwinrc --group Plugins --key mousemarkEnabled false
    kwriteconfig5 --file ~/.config/kwinrc --group Plugins --key screenedgeEnabled false
    kwriteconfig5 --file ~/.config/kwinrc --group Plugins --key trackmouseEnabled false
    
    # Mantener solo efectos esenciales
    kwriteconfig5 --file ~/.config/kwinrc --group Plugins --key kwin4_effect_maximizeEnabled true
    kwriteconfig5 --file ~/.config/kwinrc --group Plugins --key kwin4_effect_minimizeEnabled true
    kwriteconfig5 --file ~/.config/kwinrc --group Plugins --key slideEnabled true
    
    log "Efectos pesados deshabilitados."
}

# 3. OPTIMIZAR INDEXACIÓN DE ARCHIVOS
optimize_baloo() {
    log "Optimizando indexación de archivos (Baloo)..."
    
    # Deshabilitar Baloo completamente para máximo rendimiento
    kwriteconfig5 --file ~/.config/baloofilerc --group "Basic Settings" --key "Indexing-Enabled" false
    
    # Detener servicios de Baloo
    balooctl disable 2>/dev/null || true
    balooctl stop 2>/dev/null || true
    
    log "Indexación de archivos deshabilitada para mejor rendimiento."
}

# 4. OPTIMIZAR CONFIGURACIÓN DE PLASMA
optimize_plasma_settings() {
    log "Optimizando configuraciones generales de Plasma..."
    
    # Configurar animaciones más rápidas
    kwriteconfig5 --file ~/.config/kdeglobals --group KDE --key AnimationDurationFactor 0.5
    
    # Optimizar configuración del panel
    kwriteconfig5 --file ~/.config/plasmashellrc --group PlasmaViews --key panelVisibility 0
    
    # Deshabilitar efectos de escritorio innecesarios
    kwriteconfig5 --file ~/.config/kwinrc --group Desktops --key Number 1
    
    # Optimizar configuración de ventanas
    kwriteconfig5 --file ~/.config/kwinrc --group Windows --key FocusPolicy ClickToFocus
    kwriteconfig5 --file ~/.config/kwinrc --group Windows --key AutoRaise false
    kwriteconfig5 --file ~/.config/kwinrc --group Windows --key DelayFocusInterval 0
    
    log "Configuraciones de Plasma optimizadas."
}

# 5. OPTIMIZAR APLICACIONES DE INICIO
optimize_startup() {
    log "Optimizando aplicaciones de inicio..."
    
    # Crear directorio de autostart si no existe
    mkdir -p ~/.config/autostart
    
    # Lista de aplicaciones que pueden ralentizar el inicio
    APPS_TO_DISABLE=(
        "org.kde.kdeconnect.daemon.desktop"
        "org.kde.discover.notifier.desktop"
        "org.kde.plasmashell.desktop"
        "klipper.desktop"
        "org.kde.kgpg.desktop"
    )
    
    info "Aplicaciones que puedes deshabilitar del inicio:"
    for app in "${APPS_TO_DISABLE[@]}"; do
        if [ -f "/etc/xdg/autostart/$app" ] || [ -f "/usr/share/applications/$app" ]; then
            echo "  - $app"
        fi
    done
    
    warning "Para deshabilitar una aplicación del inicio, crea un archivo .desktop en ~/.config/autostart/"
    warning "Ejemplo: cp /usr/share/applications/app.desktop ~/.config/autostart/ && echo 'Hidden=true' >> ~/.config/autostart/app.desktop"
    
    log "Revisa las aplicaciones de inicio en Configuración del Sistema > Inicio y Apagado > Autostart"
}

# 6. CONFIGURAR VARIABLES DE ENTORNO
setup_environment() {
    log "Configurando variables de entorno para rendimiento..."
    
    # Crear archivo de variables de entorno
    mkdir -p ~/.config/environment.d
    
    tee ~/.config/environment.d/99-plasma-performance.conf > /dev/null << 'EOF'
# Optimizaciones de rendimiento para Plasma
QT_QPA_PLATFORMTHEME=kde
QT_AUTO_SCREEN_SCALE_FACTOR=0
QT_SCALE_FACTOR=1
QT_FONT_DPI=96

# Optimizaciones de Qt
QT_QUICK_CONTROLS_STYLE=org.kde.desktop
QT_QUICK_CONTROLS_MOBILE=false

# Optimizaciones de OpenGL
__GL_YIELD=USLEEP
__GL_THREADED_OPTIMIZATIONS=1
__GL_SYNC_TO_VBLANK=0

# Optimizaciones de KDE
KDE_SESSION_VERSION=5
KDE_FULL_SESSION=true
DESKTOP_SESSION=plasma
EOF

    log "Variables de entorno configuradas."
}

# 7. CREAR SCRIPT DE REINICIO DE PLASMA
create_plasma_restart_script() {
    log "Creando script de reinicio rápido de Plasma..."
    
    tee ~/restart_plasma.sh > /dev/null << 'EOF'
#!/bin/bash
# Script para reiniciar Plasma rápidamente

echo "Reiniciando Plasma..."

# Matar plasmashell
killall plasmashell 2>/dev/null || true

# Esperar un momento
sleep 2

# Reiniciar plasmashell
nohup plasmashell --replace > /dev/null 2>&1 &

echo "Plasma reiniciado."
EOF

    chmod +x ~/restart_plasma.sh
    log "Script de reinicio creado en ~/restart_plasma.sh"
}

# 8. VERIFICAR CONFIGURACIÓN ACTUAL
check_current_config() {
    log "Verificando configuración actual..."
    
    echo ""
    info "=== CONFIGURACIÓN ACTUAL ==="
    
    echo "Compositor habilitado: $(kreadconfig5 --file ~/.config/kwinrc --group Compositing --key Enabled)"
    echo "Backend de compositor: $(kreadconfig5 --file ~/.config/kwinrc --group Compositing --key Backend)"
    echo "Velocidad de animación: $(kreadconfig5 --file ~/.config/kwinrc --group Compositing --key AnimationSpeed)"
    echo "Baloo habilitado: $(kreadconfig5 --file ~/.config/baloofilerc --group 'Basic Settings' --key 'Indexing-Enabled')"
    
    echo ""
    info "=== USO DE MEMORIA DE PLASMA ==="
    ps aux | grep -E "(plasma|kwin)" | grep -v grep | awk '{print $11 ": " $6/1024 " MB"}'
    
    echo ""
}

# FUNCIÓN PRINCIPAL
main() {
    echo "Selecciona las optimizaciones de Plasma:"
    echo "1. Optimizar compositor"
    echo "2. Deshabilitar efectos pesados"
    echo "3. Optimizar Baloo (indexación)"
    echo "4. Optimizar configuraciones generales"
    echo "5. Revisar aplicaciones de inicio"
    echo "6. Configurar variables de entorno"
    echo "7. Crear script de reinicio"
    echo "8. Verificar configuración actual"
    echo "9. TODAS las optimizaciones"
    echo "0. Salir"
    
    read -p "Ingresa tu opción (0-9): " choice
    
    case $choice in
        1) optimize_compositing ;;
        2) disable_heavy_effects ;;
        3) optimize_baloo ;;
        4) optimize_plasma_settings ;;
        5) optimize_startup ;;
        6) setup_environment ;;
        7) create_plasma_restart_script ;;
        8) check_current_config ;;
        9) 
            optimize_compositing
            disable_heavy_effects
            optimize_baloo
            optimize_plasma_settings
            setup_environment
            create_plasma_restart_script
            ;;
        0) exit 0 ;;
        *) echo "Opción inválida" ;;
    esac
    
    log "¡Optimización de Plasma completada!"
    warning "Reinicia Plasma o el sistema para aplicar todos los cambios."
    info "Usa ~/restart_plasma.sh para reiniciar solo Plasma."
}

main "$@"
