# 📸 Tutorial Completo de Instantáneas en libvirt

## Índice
1. [Introducción](#introducción)
2. [Conceptos Básicos](#conceptos-básicos)
3. [Comandos Fundamentales](#comandos-fundamentales)
4. [<PERSON>jemplos Prácticos](#ejemplos-prácticos)
5. [Gestión Selectiva de Instantáneas](#gestión-selectiva-de-instantáneas)
6. [Mejores Prácticas](#mejores-prácticas)
7. [Solución de Problemas](#solución-de-problemas)

## Introducción

Las instantáneas (snapshots) en libvirt permiten capturar el estado completo de una máquina virtual en un momento específico, incluyendo:
- Estado de la memoria RAM
- Contenido del disco duro
- Configuración de la VM

Esto te permite revertir la VM a cualquier punto anterior, ideal para:
- Pruebas de software
- Desarrollo seguro
- Respaldos antes de actualizaciones
- Experimentación sin riesgos

## Conceptos Básicos

### Tipos de Instantáneas
- **Instantánea Interna**: Se almacena dentro del archivo de disco de la VM
- **Instantánea Externa**: Se almacena en archivos separados
- **Instantánea en Vivo**: Se toma con la VM en ejecución
- **Instantánea en Frío**: Se toma con la VM apagada (recomendado)

### Jerarquía de Instantáneas
Las instantáneas forman una cadena padre-hijo:
```
Estado Inicial → Snapshot1 → Snapshot2 → Snapshot3
```

## Comandos Fundamentales

### 1. Verificar Instantáneas Existentes
```bash
# Listar todas las instantáneas
virsh snapshot-list NOMBRE_VM

# Listar con estructura de árbol
virsh snapshot-list NOMBRE_VM --tree

# Ejemplo práctico
virsh snapshot-list Ubuntu-Dev
```

### 2. Crear Instantáneas

#### Con VM Apagada (Recomendado)
```bash
# Sintaxis básica
virsh snapshot-create-as NOMBRE_VM nombre_snapshot "Descripción detallada"

# Ejemplo práctico
virsh snapshot-create-as Ubuntu-Dev estado_inicial "Sistema limpio con 12GB RAM"
```

#### Con VM Encendida (En Vivo)
```bash
# Instantánea en vivo
virsh snapshot-create-as NOMBRE_VM nombre_snapshot "Descripción" --live

# Ejemplo práctico
virsh snapshot-create-as Ubuntu-Dev desarrollo_activo "Desarrollo en progreso" --live
```

### 3. Obtener Información de Instantáneas
```bash
# Información detallada de una instantánea
virsh snapshot-info NOMBRE_VM nombre_snapshot

# Ver instantánea actual
virsh snapshot-current NOMBRE_VM

# Ejemplo práctico
virsh snapshot-info Ubuntu-Dev estado_inicial
```

### 4. Revertir a una Instantánea
```bash
# Revertir (VM debe estar apagada)
virsh snapshot-revert NOMBRE_VM nombre_snapshot

# Revertir y arrancar automáticamente
virsh snapshot-revert NOMBRE_VM nombre_snapshot --running

# Ejemplo práctico
virsh snapshot-revert Ubuntu-Dev estado_inicial
```

### 5. Eliminar Instantáneas

#### Eliminar una Instantánea Específica
```bash
# Eliminar solo la instantánea especificada
virsh snapshot-delete NOMBRE_VM nombre_snapshot

# Ejemplo práctico
virsh snapshot-delete Ubuntu-Dev snapshot_temporal
```

#### Eliminar con Dependencias
```bash
# Eliminar instantánea y todos sus hijos
virsh snapshot-delete NOMBRE_VM nombre_snapshot --children

# Eliminar solo metadatos (mantener archivos)
virsh snapshot-delete NOMBRE_VM nombre_snapshot --metadata
```

## Ejemplos Prácticos

### Escenario 1: Flujo de Desarrollo Básico
```bash
# 1. Crear instantánea del sistema base
virsh snapshot-create-as Ubuntu-Dev base_limpia "Sistema Ubuntu recién instalado"

# 2. Instalar herramientas de desarrollo
# ... realizar instalaciones ...

# 3. Crear instantánea con herramientas
virsh snapshot-create-as Ubuntu-Dev dev_tools "Con Git, VS Code, Node.js instalados"

# 4. Desarrollar proyecto
# ... trabajar en el proyecto ...

# 5. Crear instantánea del proyecto
virsh snapshot-create-as Ubuntu-Dev proyecto_v1 "Primera versión del proyecto completa"

# 6. Si algo sale mal, revertir
virsh snapshot-revert Ubuntu-Dev dev_tools
```

### Escenario 2: Pruebas de Actualizaciones
```bash
# 1. Antes de actualizar el sistema
virsh snapshot-create-as Ubuntu-Dev pre_update "Antes de apt upgrade"

# 2. Realizar actualización
# ... ejecutar apt update && apt upgrade ...

# 3. Si la actualización causa problemas
virsh snapshot-revert Ubuntu-Dev pre_update
```

## Gestión Selectiva de Instantáneas

### Problema: Eliminar Instantáneas Específicas
**Pregunta**: Si tengo 3 instantáneas (snapshot1, snapshot2, snapshot3), ¿puedo eliminar snapshot1 y snapshot3 conservando solo snapshot2?

**Respuesta**: ✅ **SÍ, es posible**

### Método Correcto
```bash
# 1. Verificar instantáneas existentes
virsh snapshot-list Ubuntu-Dev

# Salida esperada:
# Nombre      Hora de creación            Estado
# --------------------------------------------------
# snapshot1   2025-07-03 07:47:49 -0500   shutoff
# snapshot2   2025-07-03 07:47:54 -0500   shutoff  ← LA QUE QUEREMOS CONSERVAR
# snapshot3   2025-07-03 07:47:58 -0500   shutoff

# 2. Eliminar snapshot3 (la más reciente)
virsh snapshot-delete Ubuntu-Dev snapshot3

# 3. Eliminar snapshot1 (SIN --children para conservar snapshot2)
virsh snapshot-delete Ubuntu-Dev snapshot1

# 4. Verificar que solo queda snapshot2
virsh snapshot-list Ubuntu-Dev

# 5. Activar snapshot2
virsh snapshot-revert Ubuntu-Dev snapshot2
```

### ⚠️ Advertencia Importante
```bash
# ❌ NO hagas esto si quieres conservar snapshot2:
virsh snapshot-delete Ubuntu-Dev snapshot1 --children
# Esto eliminará snapshot2 porque es "hijo" de snapshot1
```

### Verificación de Dependencias
```bash
# Ver información de dependencias antes de eliminar
virsh snapshot-info Ubuntu-Dev snapshot1

# Salida mostrará:
# Padre:          -
# Hijos:          1        ← snapshot2 depende de snapshot1
# Descendientes:  2        ← snapshot2 y snapshot3 dependen de snapshot1
```

## Mejores Prácticas

### 1. Nomenclatura Descriptiva
```bash
# ✅ Buenos nombres
virsh snapshot-create-as Ubuntu-Dev "2025-01-15_sistema_base" "Ubuntu 22.04 recién instalado"
virsh snapshot-create-as Ubuntu-Dev "2025-01-20_dev_setup" "Con herramientas de desarrollo"
virsh snapshot-create-as Ubuntu-Dev "2025-01-25_proyecto_final" "Proyecto R-exams completado"

# ❌ Malos nombres
virsh snapshot-create-as Ubuntu-Dev "snap1" "test"
virsh snapshot-create-as Ubuntu-Dev "backup" "backup"
```

### 2. Estrategia de Limpieza
```bash
# Eliminar instantáneas antiguas periódicamente
# Mantener solo las más importantes:
# - Estado base limpio
# - Hitos importantes del proyecto
# - Estado antes de cambios críticos
```

### 3. Documentación
```bash
# Usar descripciones detalladas
virsh snapshot-create-as Ubuntu-Dev milestone_1 \
  "Proyecto R-exams: Configuración inicial completa. 
   Incluye: R 4.3.0, RStudio, librerías exams, reticulate.
   Fecha: $(date). Usuario: $(whoami)"
```

### 4. Verificación Post-Creación
```bash
# Siempre verificar que la instantánea se creó correctamente
virsh snapshot-create-as Ubuntu-Dev test_snapshot "Descripción"
virsh snapshot-list Ubuntu-Dev | grep test_snapshot
virsh snapshot-info Ubuntu-Dev test_snapshot
```

## Solución de Problemas

### Error: "domain is not running"
```bash
# Problema: Intentar crear instantánea en vivo con VM apagada
# Solución: Quitar --live o encender la VM
virsh start Ubuntu-Dev
virsh snapshot-create-as Ubuntu-Dev nombre "descripción" --live
```

### Error: "Snapshot with name already exists"
```bash
# Problema: Nombre de instantánea duplicado
# Solución: Usar nombre único o eliminar la existente
virsh snapshot-delete Ubuntu-Dev nombre_existente
virsh snapshot-create-as Ubuntu-Dev nombre_existente "nueva descripción"
```

### Error: "Cannot delete snapshot with children"
```bash
# Problema: Intentar eliminar instantánea padre
# Solución: Eliminar hijos primero o usar --children
virsh snapshot-list Ubuntu-Dev --tree  # Ver jerarquía
virsh snapshot-delete Ubuntu-Dev hijo1
virsh snapshot-delete Ubuntu-Dev hijo2
virsh snapshot-delete Ubuntu-Dev padre
```

### Recuperar Espacio en Disco
```bash
# Las instantáneas pueden ocupar mucho espacio
# Verificar uso de disco
du -sh /var/lib/libvirt/images/

# Eliminar instantáneas innecesarias
virsh snapshot-list Ubuntu-Dev
virsh snapshot-delete Ubuntu-Dev instantanea_antigua
```

## Integración con Vinagre

### Conexión Post-Instantánea
1. Las instantáneas son transparentes para Vinagre
2. Después de revertir, conecta normalmente:
   ```bash
   # Revertir instantánea
   virsh snapshot-revert Ubuntu-Dev estado_deseado --running
   
   # Conectar con Vinagre (la configuración VNC se mantiene)
   vinagre localhost:5900
   ```

### Configuración VNC Persistente
- La configuración de VNC se mantiene entre instantáneas
- No necesitas reconfigurar la conexión
- El puerto VNC permanece igual

## Comandos de Referencia Rápida

```bash
# Gestión básica
virsh snapshot-list VM_NAME                    # Listar instantáneas
virsh snapshot-create-as VM_NAME NAME "DESC"   # Crear instantánea
virsh snapshot-revert VM_NAME NAME             # Revertir
virsh snapshot-delete VM_NAME NAME             # Eliminar
virsh snapshot-info VM_NAME NAME               # Ver información

# Gestión avanzada
virsh snapshot-list VM_NAME --tree             # Ver jerarquía
virsh snapshot-current VM_NAME                 # Ver actual
virsh snapshot-delete VM_NAME NAME --children  # Eliminar con hijos
virsh snapshot-revert VM_NAME NAME --running   # Revertir y arrancar
```

---

**Autor**: Tutorial generado para proyecto RepositorioMatematicasICFES_R_Exams  
**Fecha**: 2025-07-03  
**VM de ejemplo**: Ubuntu-Dev con 12GB RAM  
**Sistema**: libvirt + Vinagre en Lubuntu
