#!/bin/bash
# Configuración de optimización para VMs en Manjaro
# Optimiza el host para mejor rendimiento con VMs

set -e

echo "🔧 CONFIGURACIÓN DE OPTIMIZACIÓN PARA VMs"
echo "========================================="

# Colores
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m'

log() {
    echo -e "${GREEN}[$(date +'%H:%M:%S')] $1${NC}"
}

warning() {
    echo -e "${YELLOW}[WARNING] $1${NC}"
}

# 1. OPTIMIZACIÓN DE LIBVIRT
optimize_libvirt() {
    log "Optimizando configuración de libvirt..."
    
    # Backup de configuración actual
    sudo cp /etc/libvirt/qemu.conf /etc/libvirt/qemu.conf.backup.$(date +%Y%m%d_%H%M%S) 2>/dev/null || true
    
    # Configuraciones optimizadas para libvirt
    sudo tee -a /etc/libvirt/qemu.conf > /dev/null << 'EOF'

# Optimizaciones de rendimiento
user = "proyectos"
group = "kvm"

# Optimización de memoria
memory_backing_dir = "/dev/hugepages"
hugetlbfs_mount = "/dev/hugepages"

# Optimización de CPU
cgroup_device_acl = [
    "/dev/null", "/dev/full", "/dev/zero",
    "/dev/random", "/dev/urandom",
    "/dev/ptmx", "/dev/kvm", "/dev/kqemu",
    "/dev/rtc","/dev/hpet", "/dev/vfio/vfio"
]

# Límites de recursos
max_files = 32768
max_core = "unlimited"

# Optimización de red
bridge_helper = "/usr/lib/qemu/qemu-bridge-helper"
EOF

    log "Configuración de libvirt optimizada."
}

# 2. CONFIGURACIÓN DE HUGEPAGES
setup_hugepages() {
    log "Configurando hugepages para mejor rendimiento de VMs..."
    
    # Calcular hugepages (reservar 4GB para hugepages)
    HUGEPAGE_SIZE=2048  # 2MB por hugepage
    HUGEPAGES_COUNT=2048  # 4GB total
    
    # Configurar hugepages en sysctl
    sudo tee /etc/sysctl.d/90-hugepages.conf > /dev/null << EOF
# Configuración de hugepages para VMs
vm.nr_hugepages = $HUGEPAGES_COUNT
vm.hugetlb_shm_group = $(getent group kvm | cut -d: -f3)
EOF

    # Configurar montaje de hugepages
    if ! grep -q hugepages /etc/fstab; then
        echo "hugetlbfs /dev/hugepages hugetlbfs mode=1770,gid=$(getent group kvm | cut -d: -f3) 0 0" | sudo tee -a /etc/fstab
    fi
    
    # Crear directorio y montar
    sudo mkdir -p /dev/hugepages
    sudo mount -t hugetlbfs hugetlbfs /dev/hugepages 2>/dev/null || true
    
    log "Hugepages configuradas: $HUGEPAGES_COUNT páginas de 2MB"
}

# 3. OPTIMIZACIÓN DE CPU GOVERNOR
optimize_cpu_governor() {
    log "Configurando CPU governor para máximo rendimiento..."
    
    # Instalar cpupower si no está instalado
    if ! command -v cpupower &> /dev/null; then
        warning "Instalando cpupower..."
        sudo pacman -S --noconfirm linux-tools || true
    fi
    
    # Configurar governor en performance
    sudo cpupower frequency-set -g performance 2>/dev/null || warning "No se pudo configurar CPU governor"
    
    # Crear servicio para mantener el governor
    sudo tee /etc/systemd/system/cpu-performance.service > /dev/null << 'EOF'
[Unit]
Description=Set CPU governor to performance
After=multi-user.target

[Service]
Type=oneshot
ExecStart=/usr/bin/cpupower frequency-set -g performance
RemainAfterExit=yes

[Install]
WantedBy=multi-user.target
EOF

    sudo systemctl enable cpu-performance.service 2>/dev/null || true
    log "CPU governor configurado en modo performance."
}

# 4. OPTIMIZACIÓN DE INTERRUPCIONES (IRQ)
optimize_irq() {
    log "Optimizando distribución de interrupciones..."
    
    # Instalar irqbalance si no está instalado
    if ! command -v irqbalance &> /dev/null; then
        sudo pacman -S --noconfirm irqbalance || true
    fi
    
    # Configurar irqbalance
    sudo tee /etc/default/irqbalance > /dev/null << 'EOF'
# Configuración optimizada de irqbalance
IRQBALANCE_ARGS="--policyscript=/usr/share/irqbalance/irqbalance-policy-script"
IRQBALANCE_ONESHOT=0
IRQBALANCE_BANNED_CPUS=""
EOF

    sudo systemctl enable irqbalance
    sudo systemctl start irqbalance
    
    log "IRQ balancing optimizado."
}

# 5. CONFIGURACIÓN DE LÍMITES DEL SISTEMA
configure_limits() {
    log "Configurando límites del sistema para VMs..."
    
    # Configurar límites para el usuario
    sudo tee /etc/security/limits.d/99-vm-limits.conf > /dev/null << 'EOF'
# Límites optimizados para VMs
* soft nofile 65536
* hard nofile 65536
* soft nproc 32768
* hard nproc 32768
* soft memlock unlimited
* hard memlock unlimited
EOF

    log "Límites del sistema configurados."
}

# 6. OPTIMIZACIÓN DE GNOME BOXES
optimize_gnome_boxes() {
    log "Configurando optimizaciones para GNOME Boxes..."
    
    # Configurar variables de entorno para mejor rendimiento
    mkdir -p ~/.config/environment.d
    
    tee ~/.config/environment.d/99-vm-optimization.conf > /dev/null << 'EOF'
# Optimizaciones para VMs
QEMU_AUDIO_DRV=none
LIBVIRT_DEFAULT_URI=qemu:///system
VIRT_MANAGER_DISABLE_LIBGUESTFS=1
EOF

    log "GNOME Boxes optimizado."
}

# 7. SCRIPT DE MONITOREO DE RECURSOS
create_monitoring_script() {
    log "Creando script de monitoreo de recursos..."
    
    tee ~/vm_monitor.sh > /dev/null << 'EOF'
#!/bin/bash
# Monitor de recursos para VMs

echo "=== MONITOR DE RECURSOS PARA VMs ==="
echo "Fecha: $(date)"
echo ""

echo "=== USO DE MEMORIA ==="
free -h
echo ""

echo "=== USO DE CPU ==="
top -bn1 | grep "Cpu(s)" | awk '{print $2}' | cut -d'%' -f1 | awk '{print "CPU Usage: " $1"%"}'
echo ""

echo "=== VMs ACTIVAS ==="
echo "Libvirt VMs:"
virsh list --all 2>/dev/null || echo "libvirt no disponible"
echo ""

echo "=== PROCESOS DE VMs ==="
ps aux | grep -E "(qemu|kvm)" | grep -v grep | awk '{print $2, $3, $4, $11}' | head -5
echo ""

echo "=== HUGEPAGES ==="
cat /proc/meminfo | grep -i huge
echo ""

echo "=== I/O STATS ==="
iostat -x 1 1 2>/dev/null | tail -n +4 || echo "iostat no disponible"
EOF

    chmod +x ~/vm_monitor.sh
    log "Script de monitoreo creado en ~/vm_monitor.sh"
}

# FUNCIÓN PRINCIPAL
main() {
    echo "Selecciona las optimizaciones para VMs:"
    echo "1. Optimizar libvirt"
    echo "2. Configurar hugepages"
    echo "3. Optimizar CPU governor"
    echo "4. Optimizar IRQ balancing"
    echo "5. Configurar límites del sistema"
    echo "6. Optimizar GNOME Boxes"
    echo "7. Crear script de monitoreo"
    echo "8. TODAS las optimizaciones"
    echo "0. Salir"
    
    read -p "Ingresa tu opción (0-8): " choice
    
    case $choice in
        1) optimize_libvirt ;;
        2) setup_hugepages ;;
        3) optimize_cpu_governor ;;
        4) optimize_irq ;;
        5) configure_limits ;;
        6) optimize_gnome_boxes ;;
        7) create_monitoring_script ;;
        8) 
            optimize_libvirt
            setup_hugepages
            optimize_cpu_governor
            optimize_irq
            configure_limits
            optimize_gnome_boxes
            create_monitoring_script
            ;;
        0) exit 0 ;;
        *) echo "Opción inválida" ;;
    esac
    
    log "¡Optimización completada!"
    warning "Reinicia el sistema para aplicar todos los cambios."
}

main "$@"
